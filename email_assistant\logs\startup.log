[2025-07-12 00:20:20] [INFO] === AI Email Assistant Complete Startup Script Started ===
[2025-07-12 00:20:20] [INFO] Checking system prerequisites...
[2025-07-12 00:20:20] [SUCCESS] Python is available
[2025-07-12 00:20:20] [SUCCESS] Node.js is available
[2025-07-12 00:20:20] [SUCCESS] NPM is available
[2025-07-12 00:20:20] [SUCCESS] Rust/Cargo is available
[2025-07-12 00:20:20] [SUCCESS] Ollama found and added to PATH
[2025-07-12 00:20:20] [INFO] Starting Qdrant Vector Database in external console...
[2025-07-12 00:20:23] [SUCCESS] Qdrant Vector Database started in external console with PID 14844
[2025-07-12 00:20:23] [INFO] Waiting for Qdrant Vector Database to become healthy (max 15 seconds)...
[2025-07-12 00:20:25] [SUCCESS] Qdrant Vector Database is healthy and ready
[2025-07-12 00:20:28] [INFO] Starting RAG Service in external console...
[2025-07-12 00:20:30] [SUCCESS] RAG Service started in external console with PID 12100
[2025-07-12 00:20:30] [INFO] Waiting for RAG Service to become healthy (max 25 seconds)...
[2025-07-12 00:21:04] [SUCCESS] RAG Service is healthy and ready
[2025-07-12 00:21:07] [INFO] Starting Ingestion Service in external console...
[2025-07-12 00:21:09] [SUCCESS] Ingestion Service started in external console with PID 3820
[2025-07-12 00:21:09] [INFO] Waiting for Ingestion Service to become healthy (max 20 seconds)...
[2025-07-12 00:21:49] [WARN] Ingestion Service may not be fully ready, but continuing...
[2025-07-12 00:21:52] [INFO] Starting Desktop Application in external console...
[2025-07-12 00:21:52] [SUCCESS] Desktop Application started in external console with PID 16828
[2025-07-12 00:22:00] [SUCCESS] === Startup script completed successfully ===
[2025-07-12 00:26:39] [INFO] === AI Email Assistant Complete Startup Script Started ===
[2025-07-12 00:26:39] [INFO] Checking system prerequisites...
[2025-07-12 00:26:39] [SUCCESS] Python is available
[2025-07-12 00:26:39] [SUCCESS] Node.js is available
[2025-07-12 00:26:39] [SUCCESS] NPM is available
[2025-07-12 00:26:39] [SUCCESS] Rust/Cargo is available
[2025-07-12 00:26:39] [SUCCESS] Ollama found and added to PATH
[2025-07-12 00:26:39] [INFO] Starting Qdrant Vector Database in external console...
[2025-07-12 00:26:39] [SUCCESS] Qdrant Vector Database is already running on port 6333
[2025-07-12 00:26:42] [INFO] Starting RAG Service in external console...
[2025-07-12 00:26:42] [SUCCESS] RAG Service is already running on port 8003
[2025-07-12 00:26:45] [INFO] Starting Ingestion Service in external console...
[2025-07-12 00:26:47] [SUCCESS] Ingestion Service started in external console with PID 1492
[2025-07-12 00:26:47] [INFO] Waiting for Ingestion Service to become healthy (max 20 seconds)...
[2025-07-12 00:26:51] [SUCCESS] Ingestion Service is healthy and ready
[2025-07-12 00:26:54] [INFO] Starting Desktop Application in external console...
[2025-07-12 00:26:54] [SUCCESS] Desktop Application started in external console with PID 9032
[2025-07-12 00:27:01] [SUCCESS] === Startup script completed successfully ===
[2025-07-12 00:35:23] [INFO] === AI Email Assistant Complete Startup Script Started ===
[2025-07-12 00:35:23] [INFO] Checking system prerequisites...
[2025-07-12 00:35:23] [SUCCESS] Python is available
[2025-07-12 00:35:23] [SUCCESS] Node.js is available
[2025-07-12 00:35:23] [SUCCESS] NPM is available
[2025-07-12 00:35:23] [SUCCESS] Rust/Cargo is available
[2025-07-12 00:35:23] [SUCCESS] Ollama found and added to PATH
[2025-07-12 00:35:23] [INFO] Starting Qdrant Vector Database in external console...
[2025-07-12 00:35:23] [SUCCESS] Qdrant Vector Database is already running on port 6333
[2025-07-12 00:35:26] [INFO] Starting RAG Service in external console...
[2025-07-12 00:35:26] [SUCCESS] RAG Service is already running on port 8003
[2025-07-12 00:35:29] [INFO] Starting Ingestion Service in external console...
[2025-07-12 00:35:29] [SUCCESS] Ingestion Service is already running on port 8080
[2025-07-12 00:35:32] [INFO] Starting Desktop Application in external console...
[2025-07-12 00:35:32] [SUCCESS] Desktop Application started in external console with PID 31072
[2025-07-12 00:35:41] [SUCCESS] === Startup script completed successfully ===
